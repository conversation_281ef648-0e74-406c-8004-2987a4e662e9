/* eslint-disable @typescript-eslint/no-unused-vars */
import { api } from "@/lib/api";
import { ClassDetailsResponse } from "../screens/classes/types";

export const fetchClassesByOrgId = async ({
  orgId,
  date,
}: {
  orgId: string;
  date: string;
}) => {
  try {
    const urlParams = new URLSearchParams({
      university_id: orgId,
      date,
    }).toString();

    const response = await api
      .get<{
        classes: ClassDetailsResponse[];
      }>(
        `classes/reservations/list?${urlParams}&exclude_if_closed_or_cancelled=true&exclude_past_classes=true`
      )
      .json();

    return response?.classes;
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};
