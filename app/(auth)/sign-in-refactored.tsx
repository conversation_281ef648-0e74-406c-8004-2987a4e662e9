import React from "react";
import { View, ScrollView, KeyboardAvoidingView, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text } from "@/components/ui/text";

import { useAuth, useNavigation } from "@/hooks";
import { LoginForm, BiometricLogin, AuthHeader } from "@/components/auth";
import { TermsModal } from "@/modules/terms/terms-modal";

export const SignIn = () => {
  const {
    signIn,
    signInWithBiometric,
    biometricState,
    isLoading,
    isError,
    error,
    showTerms,
    onTermsAccepted,
  } = useAuth();

  const { navigateToForgotPassword } = useNavigation();

  const handleLogin = (values: { email: string; password: string }) => {
    signIn(values);
  };

  const handleBiometricLogin = async () => {
    await signInWithBiometric();
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        className="flex-1"
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1 justify-center px-6 py-8">
            <AuthHeader
              title="Welcome Back"
              subtitle="Sign in to your account to continue"
            />

            {/* Error Message */}
            {isError && error && (
              <View className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <Text className="text-red-600 text-center font-dm-sans-regular">
                  {error.message}
                </Text>
              </View>
            )}

            <LoginForm
              onSubmit={handleLogin}
              isLoading={isLoading}
              onForgotPassword={navigateToForgotPassword}
            />

            <BiometricLogin
              isSupported={biometricState.isSupported}
              isEnabled={biometricState.isEnabled}
              isLoading={isLoading}
              onBiometricLogin={handleBiometricLogin}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {showTerms && (
        <TermsModal isOpen={showTerms} onAccept={onTermsAccepted} />
      )}
    </SafeAreaView>
  );
};
