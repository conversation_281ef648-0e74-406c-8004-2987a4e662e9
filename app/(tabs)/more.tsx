import React from "react";
import {
  SafeAreaView,
  useSafeAreaInsets,
} from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { ScrollView, Alert, Platform } from "react-native";
import { router } from "expo-router";
import {
  User,
  Trophy,
  Award,
  Heart,
  MessageCircle,
  Flag,
  Activity,
  Calendar,
  Dumbbell,
  Zap,
  Clock,
  HelpCircle,
  ChevronRight,
} from "lucide-react-native";
import { useSession } from "@/modules/login/auth-provider";

interface MenuItemProps {
  icon: React.ElementType;
  title: string;
  onPress?: () => void;
  isLast?: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({
  icon,
  title,
  onPress,
  isLast = false,
}) => {
  return (
    <VStack>
      <Pressable
        onPress={onPress}
        className="flex-row items-center justify-between px-4 py-4 bg-white active:bg-background-50"
      >
        <HStack space="md" className="items-center flex-1">
          <Icon as={icon} size="lg" className="text-typography-500" />
          <Text className="text-base font-dm-sans-regular text-typography-700 flex-1">
            {title}
          </Text>
        </HStack>
        <Icon as={ChevronRight} size="md" className="text-typography-400" />
      </Pressable>
      {!isLast && <VStack className="h-px bg-background-100 mx-4" />}
    </VStack>
  );
};

interface MenuSectionProps {
  children: React.ReactNode;
  className?: string;
}

const MenuSection: React.FC<MenuSectionProps> = ({
  children,
  className = "",
}) => {
  return (
    <VStack
      className={`bg-white rounded-2xl mx-4 mb-6 overflow-hidden shadow-sm ${className}`}
    >
      {children}
    </VStack>
  );
};

const More = () => {
  const insets = useSafeAreaInsets();

  const { signOut } = useSession();

  // Calculate bottom padding for tab bar
  // Tab bar has min-h-[78px] + pt-4 (16px) + bottom padding (insets.bottom on iOS, 16px on Android)
  const tabBarHeight = 78 + 16 + (Platform.OS === "ios" ? insets.bottom : 16);
  const contentBottomPadding = tabBarHeight + 16; // Extra 16px for spacing

  const handleMenuPress = (item: string) => {
    // Handle specific navigation cases
    if (item === "Other programs") {
      router.push("/other-programs");
      return;
    }

    if (item === "Account") {
      return Alert.alert("Sign out", "Are you sure you want to sign out?", [
        {
          text: "Cancel",
          style: "cancel",
        },
        { text: "OK", onPress: signOut },
      ]);
    }

    // For other items, show an alert for now
    Alert.alert("Coming Soon", `${item} feature will be available soon!`);

    // TODO: Implement navigation for other menu items
    // Example navigation patterns:
    // router.push('/account') for Account
    // router.push('/leaderboard') for Leaderboard
    // etc.
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 bg-white">
          <Text className="text-2xl font-dm-sans-bold text-typography-900">
            More
          </Text>
        </HStack>

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingTop: 16,
            paddingBottom: contentBottomPadding,
          }}
        >
          {/* First Section */}
          <MenuSection>
            <MenuItem
              icon={User}
              title="Account"
              onPress={() => handleMenuPress("Account")}
            />
            <MenuItem
              icon={Trophy}
              title="Leaderboard"
              onPress={() => handleMenuPress("Leaderboard")}
            />
            <MenuItem
              icon={Award}
              title="Rewards and achievements"
              onPress={() => handleMenuPress("Rewards and achievements")}
            />
            <MenuItem
              icon={Heart}
              title="Favorites"
              onPress={() => handleMenuPress("Favorites")}
              isLast={true}
            />
          </MenuSection>

          {/* Second Section */}
          <MenuSection>
            <MenuItem
              icon={MessageCircle}
              title="Socials"
              onPress={() => handleMenuPress("Socials")}
            />
            <MenuItem
              icon={Flag}
              title="Challenges"
              onPress={() => handleMenuPress("Challenges")}
            />
            <MenuItem
              icon={Activity}
              title="Activity tracker"
              onPress={() => handleMenuPress("Activity tracker")}
            />
            <MenuItem
              icon={Calendar}
              title="Events"
              onPress={() => handleMenuPress("Events")}
            />
            <MenuItem
              icon={Dumbbell}
              title="Activities"
              onPress={() => handleMenuPress("Activities")}
              isLast={true}
            />
          </MenuSection>

          {/* Third Section */}
          <MenuSection>
            <MenuItem
              icon={Zap}
              title="Other programs"
              onPress={() => handleMenuPress("Other programs")}
            />
            <MenuItem
              icon={Clock}
              title="Facilities hours"
              onPress={() => handleMenuPress("Facilities hours")}
            />
            <MenuItem
              icon={HelpCircle}
              title="Help and support"
              onPress={() => handleMenuPress("Help and support")}
              isLast={true}
            />
          </MenuSection>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};

export default More;
