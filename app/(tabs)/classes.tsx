import React from "react";
import { VStack } from "@/components/ui/vstack";
import { SafeAreaView } from "react-native-safe-area-context";
import { ClassesHeader } from "@/components/screens/classes/classes-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import { ClassesTabs } from "@/components/screens/classes/classes-tabs";

import { useClasses } from "@/hooks";
import { ClassesList } from "@/components/classes/classes-list";
import { SearchInput } from "@/components/shared/search";

export const Classes = () => {
  const {
    selectedTab,
    selectedDate,
    searchTerm,
    refetch,
    isLoading,
    isRefetching,
    handleTabChange,
    handleDateChange,
    setSearchTerm,
    clearSearch,
    filteredData,
  } = useClasses();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ClassesTabs selectedTab={selectedTab} onTabChange={handleTabChange} />
        <VStack space="md" className="pb-4">
          {selectedTab === "classes" && (
            <VStack>
              <HorizontalDatePicker
                selectedDate={selectedDate}
                onDateSelect={handleDateChange}
              />
              <SearchInput onSearch={setSearchTerm} searchTerm={searchTerm} />
            </VStack>
          )}

          <VStack space="sm" className="px-4 bg-gray-100">
            <ClassesList
              data={filteredData}
              selectedTab={selectedTab}
              isLoading={isLoading}
              isRefreshing={isRefetching}
              searchTerm={searchTerm}
              contentBottomPadding={100}
              onRefresh={refetch}
              onClearSearch={clearSearch}
              selectedDate={selectedDate}
            />
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Classes;
