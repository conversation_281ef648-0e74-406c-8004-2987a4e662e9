import React from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Box } from "@/components/ui/box";
import { Icon } from "@/components/ui/icon";
import { Check } from "lucide-react-native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import { noop } from "lodash/fp";

interface ReservationSuccessActionsheetProps {
  isOpen: boolean;
  onClose: () => void;
  onMakeAnotherReservation: () => void;
  onAddToCalendar: () => void;
  onShareWithFriends: () => void;
  className?: string;
}

export const ReservationSuccessActionsheet: React.FC<
  ReservationSuccessActionsheetProps
> = ({
  isOpen,
  onClose,
  onMakeAnotherReservation,
  // onAddToCalendar,
  onShareWithFriends,
  className,
}) => {
  return (
    <Actionsheet isOpen={isOpen} onClose={onClose} className={className}>
      <ActionsheetBackdrop />
      <ActionsheetContent className="bg-white">
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <VStack space="lg" className="px-6 py-8 items-center">
          <Box className="w-20 h-20 rounded-full bg-green-100 items-center justify-center mb-4">
            <Icon as={Check} size="lg" className="text-green-500" />
          </Box>

          <Text className="text-2xl font-dm-sans-bold text-typography-900 text-center">
            Reservation made
          </Text>

          <Text className="text-base font-dm-sans-regular text-typography-600 text-center px-4">
            Your reservation for the selected class has been made successfully.
          </Text>

          <VStack space="sm" className="w-full mt-4">
            <Button
              onPress={onMakeAnotherReservation}
              className=" bg-[#00BFE0] rounded-full"
              variant="solid"
            >
              <ButtonText className="text-black font-dm-sans-medium text-base">
                Make another reservation
              </ButtonText>
            </Button>

            <Button
              onPress={noop}
              className="border-[#00BFE0]  rounded-full bg-[#E6F9FC]"
              variant="outline"
            >
              <ButtonText className="text-[#00BFE0] font-dm-sans-medium text-base">
                Add to calendar
              </ButtonText>
            </Button>

            <Button
              onPress={onShareWithFriends}
              className="border border-typography-300 bg-transparent rounded-full"
              variant="outline"
            >
              <ButtonText className="text-typography-600 font-dm-sans-medium text-base">
                Share with friends
              </ButtonText>
            </Button>
          </VStack>
        </VStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};
