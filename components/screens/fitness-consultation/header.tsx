import { Pressable } from "react-native";
import { useRouter } from "expo-router";
import { Box } from "@/components/ui/box";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";

import { Icon } from "@/components/ui/icon";
import { ArrowLeft } from "iconsax-react-nativejs";
import { Text } from "@/components/ui/text";

export const AppointmentHeader = () => {
  const router = useRouter();
  return (
    <Box className="bg-background-0 px-4 pb-2">
      <VStack className="gap-2">
        <HStack className="items-center py-4 bg-white" space="xs">
          <Pressable
            onPress={() => router.back()}
            className="w-10 h-10 items-center justify-center rounded-full bg-background-100"
          >
            <Icon
              as={() => <ArrowLeft color="black" />}
              className="text-typography-900"
            />
          </Pressable>

          <Text className="text-lg font-dm-sans-bold text-typography-900 flex-1">
            Fitness Consultation
          </Text>
        </HStack>
      </VStack>
    </Box>
  );
};
