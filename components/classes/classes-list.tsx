import React from "react";
import { FlatList, RefreshControl } from "react-native";
import { ClassCard } from "@/components/screens/classes/class-card/class-card";

import { ClassCardSkeleton } from "@/components/screens/classes/class-card/class-skeleton";
import { EmptyState } from "@/components/screens/classes/empty-state";
import { EmptySearchIcon } from "@/components/shared/icon/empty-search";
import { Button, ButtonText } from "@/components/ui/button";
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import { CategoryType } from "@/data/screens/appointments/types";
import { CategoryCard } from "../screens/appointments/category-card";

interface ClassesListProps {
  data: ClassDetailsResponse[] | CategoryType[] | undefined;
  selectedTab: "classes" | "appointment";
  isLoading: boolean;
  isRefreshing: boolean;
  searchTerm: string;
  contentBottomPadding: number;
  onRefresh: () => void;
  onClearSearch: () => void;
  selectedDate: Date;
}

const skeletonData = Array.from({ length: 5 }, (_, index) => ({ id: index }));

const RenderEmptyState = ({
  isLoading,
  isEmpty,
  searchTerm,
  onClearSearch,
  contentBottomPadding,
}: {
  isLoading: boolean;
  isEmpty: boolean;
  searchTerm?: string;
  onClearSearch: () => void;
  contentBottomPadding: number;
}) => {
  if (isLoading) {
    return (
      <FlatList
        data={skeletonData}
        renderItem={() => <ClassCardSkeleton />}
        keyExtractor={(item) => String(item.id)}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: contentBottomPadding,
        }}
      />
    );
  }

  if (isEmpty && searchTerm) {
    return (
      <EmptyState
        icon={<EmptySearchIcon />}
        title="No results found"
        subtitle={`We couldn't find any results for "${searchTerm}". Try adjusting your search.`}
        action={
          <Button
            variant="outline"
            className="border-[#00697B] rounded-full"
            onPress={onClearSearch}
          >
            <ButtonText className="text-[#00697B]">Clear search</ButtonText>
          </Button>
        }
      />
    );
  }

  if (isEmpty) {
    return (
      <EmptyState
        title="No classes available"
        subtitle="There are no classes scheduled for this date. Please select a different date."
      />
    );
  }

  return null;
};

const RenderItem = ({
  item,
  selectedTab,
  selectedDate,
}: {
  item: ClassDetailsResponse | CategoryType;
  selectedTab: "classes" | "appointment";
  selectedDate: Date;
}) => {
  if (selectedTab === "classes") {
    return (
      <ClassCard
        {...(item as ClassDetailsResponse)}
        selectedDate={selectedDate}
      />
    );
  } else {
    return <CategoryCard {...(item as CategoryType)} />;
  }
};

export const ClassesList: React.FC<ClassesListProps> = ({
  data,
  selectedTab,
  isLoading,
  isRefreshing,
  searchTerm,
  contentBottomPadding,
  onRefresh,
  onClearSearch,
  selectedDate,
}) => {
  const isEmpty = !data?.length;

  if (isLoading || isEmpty) {
    return (
      <RenderEmptyState
        isLoading={isLoading}
        isEmpty={isEmpty}
        searchTerm={searchTerm}
        onClearSearch={onClearSearch}
        contentBottomPadding={contentBottomPadding}
      />
    );
  }

  return (
    <FlatList
      className="pt-5"
      data={data}
      renderItem={({ item }) => (
        <RenderItem
          item={item}
          selectedTab={selectedTab}
          selectedDate={selectedDate}
        />
      )}
      keyExtractor={(item: ClassDetailsResponse | CategoryType) =>
        String(item.id)
      }
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          tintColor="#00697B"
          colors={["#00697B"]}
        />
      }
      contentContainerStyle={{
        paddingBottom: 400,
      }}
    />
  );
};
