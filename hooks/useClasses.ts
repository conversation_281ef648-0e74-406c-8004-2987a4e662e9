import { useCallback, useMemo, useState } from "react";
import { matchSorter } from "match-sorter";
import { useClassesQuery } from "@/data/screens/classes/queries/useClassesQuery";
import { formatDate } from "@/data/common/common.utils";
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import { CategoryType } from "@/data/screens/appointments/types";
import { useCategoriesAppointments } from "@/data/screens/appointments/queries/useCategoriesAppointments";

export const useClasses = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Fetch classes data
  const {
    data: classesData = [],
    isLoading: isClassesLoading,
    error: classesQueryError,
    refetch: refetchClasses,
    isRefetching: isClassesRefetching,
  } = useClassesQuery({
    date: formatDate(selectedDate),
  });

  const {
    data: categoriesData,
    isLoading: isCategoriesLoading,
    error: categoriesQueryError,
    isRefetching: isCategoriesRefetching,
    refetch: refetchCategories,
  } = useCategoriesAppointments();

  const currentRecord = useMemo(() => {
    if (selectedTab === "classes") {
      return {
        data: classesData,
        error: classesQueryError,
        isLoading: isClassesLoading,
        isRefetching: isClassesRefetching,
      };
    }
    return {
      data: categoriesData,
      error: categoriesQueryError,
      isLoading: isCategoriesLoading,
      isRefetching: isCategoriesRefetching,
    };
  }, [
    selectedTab,
    categoriesData,
    categoriesQueryError,
    isCategoriesLoading,
    isCategoriesRefetching,
    classesData,
    classesQueryError,
    isClassesLoading,
    isClassesRefetching,
  ]);

  const filteredData = useCallback(() => {
    if (!searchTerm) return currentRecord.data;

    if (selectedTab === "classes") {
      return matchSorter(
        (currentRecord?.data as ClassDetailsResponse[]) ?? [],
        searchTerm,
        {
          keys: [
            "name",
            "room_name",
            "gym_name",
            "instructor_first_name",
            "instructor_last_name",
            "start_time",
          ],
        }
      );
    } else {
      return matchSorter(
        (currentRecord?.data as CategoryType[]) ?? [],
        searchTerm,
        {
          keys: ["name", "gym_name", "room_name", "reservations_count"],
        }
      );
    }
  }, [searchTerm, currentRecord, selectedTab]);

  const refetch = useCallback(() => {
    if (selectedTab === "classes") {
      return refetchClasses();
    } else {
      return refetchCategories();
    }
  }, [selectedTab, refetchClasses, refetchCategories]);

  // Handle tab change
  const handleTabChange = useCallback((tab: "classes" | "appointment") => {
    setSelectedTab(tab);
    setSearchTerm("");
  }, []);

  // Handle date change
  const handleDateChange = useCallback(
    (date: Date) => {
      setSelectedDate(date);
    },
    [setSelectedDate]
  );

  return {
    filteredData: filteredData(),
    ...currentRecord,

    // UI State
    selectedTab,
    selectedDate,
    searchTerm,

    // Actions
    setSearchTerm,
    clearSearch: () => setSearchTerm(""),
    handleTabChange,
    handleDateChange,
    refetch,
  };
};
